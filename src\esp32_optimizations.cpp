#include "esp32_optimizations.h"
#include "config.h"
#include <nvs_flash.h>
#include <nvs.h>

// Global instances
ESP32Optimizations esp32opt;
ESP32HAL esp32hal;

// Static member initialization
esp_adc_cal_characteristics_t ESP32Optimizations::adc_chars_voltage;
esp_adc_cal_characteristics_t ESP32Optimizations::adc_chars_current;
bool ESP32Optimizations::adc_calibrated = false;

// ============================================================================
// ESP32Optimizations Implementation
// ============================================================================

void ESP32Optimizations::initializeESP32() {
  DEBUG_PRINTLN("Initializing ESP32 optimizations...");
  
  // Disable brownout detector for stable operation
  WRITE_PERI_REG(RTC_CNTL_BROWN_OUT_REG, 0);
  
  // Set CPU frequency to maximum for best performance
  setCpuFrequencyMhz(ESP32_CPU_FREQ_240MHZ);
  
  // Initialize NVS for configuration storage
  esp_err_t ret = nvs_flash_init();
  if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
    ESP_ERROR_CHECK(nvs_flash_erase());
    ret = nvs_flash_init();
  }
  ESP_ERROR_CHECK(ret);
  
  // Disable unused peripherals
  disableUnusedPeripherals();
  
  // Optimize WiFi settings
  optimizeWiFiSettings();
  
  // Setup power management
  setupPowerManagement();
  
  // Calibrate ADC
  calibrateADC();
  
  DEBUG_PRINTLN("ESP32 optimizations initialized");
}

void ESP32Optimizations::disableUnusedPeripherals() {
  // Disable Bluetooth to save power and memory
  esp_bt_controller_disable();
  
  // Disable unused ADC2 (used by WiFi)
  // ADC1 is used for our sensors
  
  DEBUG_PRINTLN("Unused peripherals disabled");
}

void ESP32Optimizations::optimizeWiFiSettings() {
  // Set WiFi to station mode only
  WiFi.mode(WIFI_STA);
  
  // Optimize WiFi power settings
  esp_wifi_set_ps(WIFI_PS_MIN_MODEM);  // Minimum power save mode
  
  // Set WiFi bandwidth to 20MHz for better stability
  esp_wifi_set_bandwidth(WIFI_IF_STA, WIFI_BW_HT20);
  
  DEBUG_PRINTLN("WiFi settings optimized");
}

void ESP32Optimizations::setupPowerManagement() {
  // Configure automatic light sleep (saves power when idle)
  esp_pm_config_esp32_t pm_config = {
    .max_freq_mhz = ESP32_CPU_FREQ_240MHZ,
    .min_freq_mhz = 80,  // Minimum frequency when idle
    .light_sleep_enable = true
  };
  
  esp_pm_configure(&pm_config);
  
  DEBUG_PRINTLN("Power management configured");
}

void ESP32Optimizations::calibrateADC() {
  // Calibrate ADC for voltage sensor
  esp_adc_cal_value_t val_type = esp_adc_cal_characterize(
    ADC_UNIT_1, 
    ADC_ATTENUATION, 
    ADC_WIDTH, 
    ESP32_ADC_VREF_DEFAULT, 
    &adc_chars_voltage
  );
  
  // Calibrate ADC for current sensor
  esp_adc_cal_characterize(
    ADC_UNIT_1, 
    ADC_ATTENUATION, 
    ADC_WIDTH, 
    ESP32_ADC_VREF_DEFAULT, 
    &adc_chars_current
  );
  
  // Print calibration method used
  if (val_type == ESP_ADC_CAL_VAL_EFUSE_VREF) {
    DEBUG_PRINTLN("ADC calibrated using eFuse Vref");
  } else if (val_type == ESP_ADC_CAL_VAL_EFUSE_TP) {
    DEBUG_PRINTLN("ADC calibrated using Two Point values");
  } else {
    DEBUG_PRINTLN("ADC calibrated using default Vref");
  }
  
  adc_calibrated = true;
}

uint32_t ESP32Optimizations::readVoltageCalibrated(adc1_channel_t channel) {
  if (!adc_calibrated) {
    calibrateADC();
  }
  
  // Take multiple samples for better accuracy
  uint32_t adc_reading = 0;
  for (int i = 0; i < ESP32_ADC_SAMPLES_AVG; i++) {
    adc_reading += adc1_get_raw(channel);
  }
  adc_reading /= ESP32_ADC_SAMPLES_AVG;
  
  // Convert to voltage using calibration
  uint32_t voltage = esp_adc_cal_raw_to_voltage(adc_reading, &adc_chars_voltage);
  
  return voltage;  // Returns voltage in mV
}

uint32_t ESP32Optimizations::readCurrentCalibrated(adc1_channel_t channel) {
  if (!adc_calibrated) {
    calibrateADC();
  }
  
  // Take multiple samples for better accuracy
  uint32_t adc_reading = 0;
  for (int i = 0; i < ESP32_ADC_SAMPLES_AVG; i++) {
    adc_reading += adc1_get_raw(channel);
  }
  adc_reading /= ESP32_ADC_SAMPLES_AVG;
  
  // Convert to voltage using calibration
  uint32_t voltage = esp_adc_cal_raw_to_voltage(adc_reading, &adc_chars_current);
  
  return voltage;  // Returns voltage in mV
}

void ESP32Optimizations::printMemoryInfo() {
  Serial.println("=== ESP32 Memory Information ===");
  Serial.printf("Free Heap: %d bytes\n", ESP.getFreeHeap());
  Serial.printf("Min Free Heap: %d bytes\n", ESP.getMinFreeHeap());
  Serial.printf("Max Alloc Heap: %d bytes\n", ESP.getMaxAllocHeap());
  Serial.printf("Heap Size: %d bytes\n", ESP.getHeapSize());
  
  if (ESP.getPsramSize() > 0) {
    Serial.printf("PSRAM Size: %d bytes\n", ESP.getPsramSize());
    Serial.printf("Free PSRAM: %d bytes\n", ESP.getFreePsram());
  } else {
    Serial.println("PSRAM: Not available");
  }
  
  Serial.printf("Flash Size: %d bytes\n", ESP.getFlashChipSize());
  Serial.printf("Sketch Size: %d bytes\n", ESP.getSketchSize());
  Serial.printf("Free Sketch Space: %d bytes\n", ESP.getFreeSketchSpace());
  Serial.println("===============================");
}

bool ESP32Optimizations::checkMemoryHealth() {
  uint32_t free_heap = ESP.getFreeHeap();
  
  if (free_heap < ESP32_MIN_FREE_HEAP) {
    DEBUG_PRINTF("WARNING: Low memory! Free heap: %d bytes\n", free_heap);
    return false;
  }
  
  if (free_heap < ESP32_HEAP_WARNING) {
    DEBUG_PRINTF("CAUTION: Memory getting low. Free heap: %d bytes\n", free_heap);
  }
  
  return true;
}

void ESP32Optimizations::optimizeHeap() {
  // Force garbage collection
  ESP.getMaxAllocHeap();
  
  // Compact heap if possible (implementation depends on heap allocator)
  DEBUG_PRINTLN("Heap optimization attempted");
}

void ESP32Optimizations::printCPUInfo() {
  Serial.println("=== ESP32 CPU Information ===");
  Serial.printf("Chip Model: %s\n", ESP.getChipModel());
  Serial.printf("Chip Revision: %d\n", ESP.getChipRevision());
  Serial.printf("CPU Cores: %d\n", ESP.getChipCores());
  Serial.printf("CPU Frequency: %d MHz\n", ESP.getCpuFreqMHz());
  Serial.printf("APB Frequency: %d Hz\n", getApbFrequency());
  Serial.printf("Flash Speed: %d MHz\n", ESP.getFlashChipSpeed() / 1000000);
  Serial.printf("Flash Mode: %d\n", ESP.getFlashChipMode());
  Serial.printf("Temperature: %.1f°C\n", temperatureRead());
  Serial.println("=============================");
}

void ESP32Optimizations::printTaskInfo() {
  Serial.println("=== ESP32 Task Information ===");
  Serial.printf("Current Task: %s\n", pcTaskGetTaskName(NULL));
  Serial.printf("Stack High Water Mark: %d bytes\n", uxTaskGetStackHighWaterMark(NULL));
  Serial.printf("Number of Tasks: %d\n", uxTaskGetNumberOfTasks());
  Serial.println("==============================");
}

uint32_t ESP32Optimizations::getFreeStackSpace() {
  return uxTaskGetStackHighWaterMark(NULL);
}

void ESP32Optimizations::feedTaskWatchdog() {
  esp_task_wdt_reset();
}

void ESP32Optimizations::setupCustomWatchdog() {
  // Custom watchdog setup if needed
  esp_task_wdt_init(WATCHDOG_TIMEOUT / 1000, true);
  esp_task_wdt_add(NULL);
}

void ESP32Optimizations::logSystemRestart() {
  esp_reset_reason_t reset_reason = esp_reset_reason();
  
  Serial.print("ESP32 Reset Reason: ");
  switch (reset_reason) {
    case ESP_RST_POWERON:   Serial.println("Power-on reset"); break;
    case ESP_RST_EXT:       Serial.println("External reset"); break;
    case ESP_RST_SW:        Serial.println("Software reset"); break;
    case ESP_RST_PANIC:     Serial.println("Exception/panic reset"); break;
    case ESP_RST_INT_WDT:   Serial.println("Interrupt watchdog reset"); break;
    case ESP_RST_TASK_WDT:  Serial.println("Task watchdog reset"); break;
    case ESP_RST_WDT:       Serial.println("Other watchdog reset"); break;
    case ESP_RST_DEEPSLEEP: Serial.println("Deep sleep reset"); break;
    case ESP_RST_BROWNOUT:  Serial.println("Brownout reset"); break;
    case ESP_RST_SDIO:      Serial.println("SDIO reset"); break;
    default:                Serial.println("Unknown reset"); break;
  }
}

// ============================================================================
// ESP32HAL Implementation
// ============================================================================

void ESP32HAL::configureGPIOForADC(gpio_num_t pin) {
  adc1_config_width(ADC_WIDTH);
  adc1_config_channel_atten((adc1_channel_t)pin, ADC_ATTENUATION);
}

bool ESP32HAL::writeToNVS(const char* key, const void* data, size_t length) {
  nvs_handle_t nvs_handle;
  esp_err_t err = nvs_open("storage", NVS_READWRITE, &nvs_handle);
  if (err != ESP_OK) return false;
  
  err = nvs_set_blob(nvs_handle, key, data, length);
  if (err != ESP_OK) {
    nvs_close(nvs_handle);
    return false;
  }
  
  err = nvs_commit(nvs_handle);
  nvs_close(nvs_handle);
  
  return (err == ESP_OK);
}

bool ESP32HAL::readFromNVS(const char* key, void* data, size_t length) {
  nvs_handle_t nvs_handle;
  esp_err_t err = nvs_open("storage", NVS_READONLY, &nvs_handle);
  if (err != ESP_OK) return false;
  
  size_t required_size = length;
  err = nvs_get_blob(nvs_handle, key, data, &required_size);
  nvs_close(nvs_handle);
  
  return (err == ESP_OK && required_size == length);
}

float ESP32HAL::getCPUTemperature() {
  return temperatureRead();
}

bool ESP32HAL::isTemperatureNormal() {
  float temp = getCPUTemperature();
  return (temp > -10.0 && temp < 85.0);  // Normal operating range
}
