#include "wifi_manager.h"

// Global WiFi manager instance
PowerWiFiManager wifiMgr;

// ============================================================================
// PowerWiFiManager Implementation
// ============================================================================

PowerWiFiManager::PowerWiFiManager() : server(80) {
  wifi_connected = false;
  config_portal_running = false;
  last_connection_attempt = 0;
  connection_retry_count = 0;

  blynk_token = "";
  blynk_server = "blynk.cloud";
  device_name = "PowerMonitor";
}

bool PowerWiFiManager::begin() {
  DEBUG_PRINTLN("Initializing WiFi Manager...");
  
  // Initialize SPIFFS for configuration storage
  if (!SPIFFS.begin(true)) {
    DEBUG_PRINTLN("SPIFFS initialization failed");
    return false;
  }
  
  // Load saved configuration
  loadConfiguration();
  
  // Setup custom parameters
  setupCustomParameters();
  
  // Configure WiFiManager
  wifiManager.setSaveConfigCallback([]() {
    shouldSaveConfig = true;
  });
  
  wifiManager.setConfigPortalTimeout(WIFI_CONFIG_PORTAL_TIMEOUT);
  wifiManager.setConnectTimeout(30);
  wifiManager.setDebugOutput(true);
  
  // Set hostname
  WiFi.setHostname(WIFI_HOSTNAME);
  
  DEBUG_PRINTLN("WiFi Manager initialized successfully");
  return true;
}

void PowerWiFiManager::setupCustomParameters() {
  // Simplified setup - parameters will be handled via web interface
  DEBUG_PRINTLN("Custom parameters setup completed");
}

bool PowerWiFiManager::connectWiFi() {
  DEBUG_PRINTLN("Attempting WiFi connection...");

  // Load saved credentials
  loadConfiguration();

  // Try to connect with saved credentials
  if (blynk_token.length() > 0) {
    WiFi.begin("YourWiFiSSID", "YourWiFiPassword"); // Replace with actual credentials

    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 20) {
      delay(500);
      attempts++;
    }

    if (WiFi.status() == WL_CONNECTED) {
      wifi_connected = true;
      connection_retry_count = 0;
      DEBUG_PRINTF("WiFi connected! IP: %s\n", WiFi.localIP().toString().c_str());
      return true;
    }
  }

  wifi_connected = false;
  connection_retry_count++;
  DEBUG_PRINTLN("WiFi connection failed");
  return false;
}

void PowerWiFiManager::handleConnection() {
  if (WiFi.status() != WL_CONNECTED) {
    wifi_connected = false;
    
    // Attempt reconnection if not too many retries
    if (connection_retry_count < MAX_WIFI_RETRIES) {
      unsigned long current_time = millis();
      if (current_time - last_connection_attempt > ERROR_RECOVERY_DELAY) {
        DEBUG_PRINTLN("WiFi disconnected, attempting reconnection...");
        reconnect();
        last_connection_attempt = current_time;
      }
    }
  } else {
    if (!wifi_connected) {
      wifi_connected = true;
      connection_retry_count = 0;
      DEBUG_PRINTLN("WiFi reconnected successfully");
    }
  }
}

void PowerWiFiManager::reconnect() {
  WiFi.disconnect();
  delay(1000);
  WiFi.reconnect();
  connection_retry_count++;
}

bool PowerWiFiManager::loadConfiguration() {
  DEBUG_PRINTLN("Loading configuration from SPIFFS...");
  
  File configFile = SPIFFS.open("/config.json", "r");
  if (!configFile) {
    DEBUG_PRINTLN("No configuration file found, using defaults");
    return false;
  }
  
  size_t size = configFile.size();
  if (size > 1024) {
    DEBUG_PRINTLN("Configuration file too large");
    configFile.close();
    return false;
  }
  
  std::unique_ptr<char[]> buf(new char[size]);
  configFile.readBytes(buf.get(), size);
  configFile.close();
  
  DynamicJsonDocument doc(1024);
  auto error = deserializeJson(doc, buf.get());
  
  if (error) {
    DEBUG_PRINTF("Failed to parse configuration: %s\n", error.c_str());
    return false;
  }
  
  // Load values
  blynk_token = doc["blynk_token"] | "";
  blynk_server = doc["blynk_server"] | "blynk.cloud";
  device_name = doc["device_name"] | "PowerMonitor";
  
  DEBUG_PRINTLN("Configuration loaded successfully");
  return true;
}

bool PowerWiFiManager::saveConfiguration() {
  DEBUG_PRINTLN("Saving configuration to SPIFFS...");
  
  DynamicJsonDocument doc(1024);
  doc["blynk_token"] = blynk_token;
  doc["blynk_server"] = blynk_server;
  doc["device_name"] = device_name;
  
  File configFile = SPIFFS.open("/config.json", "w");
  if (!configFile) {
    DEBUG_PRINTLN("Failed to open config file for writing");
    return false;
  }
  
  if (serializeJson(doc, configFile) == 0) {
    DEBUG_PRINTLN("Failed to write configuration");
    configFile.close();
    return false;
  }
  
  configFile.close();
  DEBUG_PRINTLN("Configuration saved successfully");
  return true;
}

void PowerWiFiManager::startConfigPortal() {
  DEBUG_PRINTLN("Starting configuration portal...");
  config_portal_running = true;

  // Start WiFi AP mode
  WiFi.mode(WIFI_AP);
  WiFi.softAP(WIFI_AP_NAME, WIFI_AP_PASSWORD);

  DEBUG_PRINTF("AP IP address: %s\n", WiFi.softAPIP().toString().c_str());
}

void PowerWiFiManager::resetSettings() {
  DEBUG_PRINTLN("Resetting WiFi settings...");

  // Reset configuration
  blynk_token = "";
  blynk_server = "blynk.cloud";
  device_name = "PowerMonitor";

  // Remove config file
  if (SPIFFS.exists("/config.json")) {
    SPIFFS.remove("/config.json");
  }

  DEBUG_PRINTLN("Settings reset complete");
}

String PowerWiFiManager::getWiFiStatus() const {
  switch (WiFi.status()) {
    case WL_CONNECTED:
      return "Connected";
    case WL_NO_SSID_AVAIL:
      return "No SSID Available";
    case WL_CONNECT_FAILED:
      return "Connection Failed";
    case WL_CONNECTION_LOST:
      return "Connection Lost";
    case WL_DISCONNECTED:
      return "Disconnected";
    default:
      return "Unknown";
  }
}

String PowerWiFiManager::getIPAddress() const {
  if (wifi_connected) {
    return WiFi.localIP().toString();
  }
  return "0.0.0.0";
}

int PowerWiFiManager::getSignalStrength() const {
  if (wifi_connected) {
    return WiFi.RSSI();
  }
  return -100;
}

bool PowerWiFiManager::shouldSaveConfig() const {
  return false; // Simplified for now
}

// Static callback functions
void PowerWiFiManager::configModeCallback(WiFiManager *myWiFiManager) {
  DEBUG_PRINTLN("Entered config mode");
  DEBUG_PRINTF("Config AP IP: %s\n", WiFi.softAPIP().toString().c_str());
}

void PowerWiFiManager::saveConfigCallback() {
  DEBUG_PRINTLN("Save config callback");
}
