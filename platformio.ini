; PlatformIO Project Configuration File for ESP32 Power Monitoring System
;
; This configuration includes all necessary libraries for:
; - WiFi Management with captive portal
; - Blynk IoT Cloud integration
; - Sensor reading and filtering
; - Real-time monitoring and alerts
;
; Hardware: ESP32 DevKit v1 with ZMPT101B voltage sensor and ACS712 current sensor

[env:esp32doit-devkit-v1]
platform = espressif32
board = esp32doit-devkit-v1
framework = arduino

; Build settings optimized for ESP32
build_flags =
    -DCORE_DEBUG_LEVEL=3
    -DCONFIG_ARDUHAL_LOG_COLORS=1
    -DBLYNK_PRINT=Serial
    -DBLYNK_DEBUG
    -DBOARD_HAS_PSRAM
    -DCONFIG_SPIRAM_SUPPORT=1
    -DCONFIG_SPIRAM_USE_MALLOC=1
    -DCONFIG_SPIRAM_CACHE_WORKAROUND=1
    -DARDUINO_RUNNING_CORE=1
    -DARDUINO_EVENT_RUNNING_CORE=1
    -DESP32_DEV=1

; Monitor settings
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

; Library dependencies
lib_deps =
    ; WiFi Management
    tzapu/WiFiManager@^0.16.0

    ; Blynk IoT Platform
    blynkkk/Blynk@^1.3.2

    ; JSON handling for configuration
    bblanchon/ArduinoJson@^7.0.4

    ; Time synchronization
    paulstoffregen/Time@^1.6.1

    ; Sensor filtering and smoothing
    dxinteractive/ResponsiveAnalogRead@^1.2.1

    ; EEPROM preferences
    arduino-libraries/Arduino_JSON@^0.2.0

; Upload settings optimized for ESP32
upload_speed = 921600
upload_port = AUTO
upload_protocol = esptool

; Board configuration for ESP32
board_build.partitions = huge_app.csv
board_build.filesystem = spiffs
board_build.flash_mode = dio
board_build.f_cpu = 240000000L
board_build.f_flash = 80000000L

; Debugging
debug_tool = esp-prog
debug_init_break = tbreak setup
debug_speed = 20000
