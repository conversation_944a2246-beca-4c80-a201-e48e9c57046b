; PlatformIO Project Configuration File for ESP32 Power Monitoring System
;
; This configuration includes all necessary libraries for:
; - WiFi Management with captive portal
; - Blynk IoT Cloud integration
; - Sensor reading and filtering
; - Real-time monitoring and alerts
;
; Hardware: ESP32 DevKit v1 with ZMPT101B voltage sensor and ACS712 current sensor

[env:esp32doit-devkit-v1]
platform = espressif32
board = esp32doit-devkit-v1
framework = arduino

; Build settings for optimal performance
build_flags =
    -DCORE_DEBUG_LEVEL=3
    -DCONFIG_ARDUHAL_LOG_COLORS=1
    -DBLYNK_PRINT=Serial
    -DBLYNK_DEBUG

; Monitor settings
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

; Library dependencies
lib_deps =
    ; WiFi Management
    tzapu/WiFiManager@^0.16.0

    ; Blynk IoT Platform
    blynkkk/Blynk@^1.3.2

    ; JSON handling for configuration
    bblanchon/Arduino<PERSON>son@^7.0.4

    ; Time synchronization
    paulstoffregen/Time@^1.6.1

    ; Sensor filtering and smoothing
    dxinteractive/ResponsiveAnalogRead@^1.2.1

    ; EEPROM preferences
    arduino-libraries/Arduino_JSON@^0.2.0

; Upload settings
upload_speed = 921600
upload_port = AUTO

; Debugging
debug_tool = esp-prog
debug_init_break = tbreak setup
