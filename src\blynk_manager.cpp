#include "blynk_manager.h"
#include "alert_system.h"
#include "sensors.h"

// Global Blynk manager instance
PowerBlynkManager blynkMgr;

// External references
extern AlertSystem alertSys;
extern PowerSensors sensors;

// ============================================================================
// PowerBlynkManager Implementation
// ============================================================================

PowerBlynkManager::PowerBlynkManager() {
  blynk_connected = false;
  blynk_initialized = false;
  last_send_time = 0;
  last_heartbeat = 0;
  connection_retry_count = 0;
  
  auth_token = "";
  server_address = "blynk.cloud";
  server_port = 80;
  
  last_voltage_sent = 0;
  last_current_sent = 0;
  last_power_sent = 0;
  force_update = false;
  
  // Initialize alert tracking
  for (int i = 0; i < 10; i++) {
    alert_sent[i] = false;
    alert_cooldown[i] = 0;
  }
}

bool PowerBlynkManager::begin(const String& token, const String& server, int port) {
  if (token.length() == 0) {
    DEBUG_PRINTLN("No Blynk token provided");
    return false;
  }
  
  auth_token = token;
  server_address = server;
  server_port = port;
  
  DEBUG_PRINTF("Initializing Blynk with server: %s:%d\n", server.c_str(), port);
  
  // Configure Blynk
  Blynk.config(auth_token.c_str(), server_address.c_str(), server_port);
  
  blynk_initialized = true;
  return true;
}

bool PowerBlynkManager::connect() {
  if (!blynk_initialized) {
    DEBUG_PRINTLN("Blynk not initialized");
    return false;
  }
  
  DEBUG_PRINTLN("Connecting to Blynk...");
  
  // Attempt connection with timeout
  unsigned long start_time = millis();
  while (!Blynk.connected() && (millis() - start_time) < BLYNK_CONNECTION_TIMEOUT) {
    Blynk.connect();
    delay(100);
  }
  
  if (Blynk.connected()) {
    blynk_connected = true;
    connection_retry_count = 0;
    DEBUG_PRINTLN("Blynk connected successfully");
    
    // Sync virtual pins
    sync();
    
    return true;
  } else {
    blynk_connected = false;
    connection_retry_count++;
    DEBUG_PRINTLN("Blynk connection failed");
    return false;
  }
}

void PowerBlynkManager::handleConnection() {
  if (!blynk_initialized) return;
  
  if (!Blynk.connected()) {
    blynk_connected = false;
    
    // Attempt reconnection if not too many retries
    if (connection_retry_count < MAX_BLYNK_RETRIES) {
      unsigned long current_time = millis();
      if (current_time - last_heartbeat > ERROR_RECOVERY_DELAY) {
        DEBUG_PRINTLN("Blynk disconnected, attempting reconnection...");
        reconnect();
        last_heartbeat = current_time;
      }
    }
  } else {
    if (!blynk_connected) {
      blynk_connected = true;
      connection_retry_count = 0;
      DEBUG_PRINTLN("Blynk reconnected successfully");
    }
  }
}

void PowerBlynkManager::reconnect() {
  if (blynk_initialized) {
    Blynk.disconnect();
    delay(1000);
    connect();
  }
}

void PowerBlynkManager::disconnect() {
  if (blynk_connected) {
    Blynk.disconnect();
    blynk_connected = false;
    DEBUG_PRINTLN("Blynk disconnected");
  }
}

void PowerBlynkManager::sendData(float voltage, float current, float power, float energy) {
  if (!blynk_connected || !isTimeToSend()) {
    return;
  }
  
  if (shouldSendUpdate(voltage, current, power) || force_update) {
    sendSensorData(voltage, current, power, energy);
    handleAlerts(voltage, current, power);
    
    last_voltage_sent = voltage;
    last_current_sent = current;
    last_power_sent = power;
    last_send_time = millis();
    force_update = false;
  }
}

bool PowerBlynkManager::shouldSendUpdate(float voltage, float current, float power) {
  // Send if significant change in any value
  float voltage_change = abs(voltage - last_voltage_sent);
  float current_change = abs(current - last_current_sent);
  float power_change = abs(power - last_power_sent);
  
  return (voltage_change > 1.0) ||   // 1V change
         (current_change > 0.1) ||   // 0.1A change
         (power_change > 10.0);      // 10W change
}

void PowerBlynkManager::sendSensorData(float voltage, float current, float power, float energy) {
  Blynk.virtualWrite(VPIN_VOLTAGE, voltage);
  Blynk.virtualWrite(VPIN_CURRENT, current);
  Blynk.virtualWrite(VPIN_POWER, power);
  Blynk.virtualWrite(VPIN_ENERGY, energy);
  
  DEBUG_PRINTF("Sent to Blynk - V:%.1fV, I:%.2fA, P:%.1fW, E:%.2fWh\n", 
               voltage, current, power, energy);
}

void PowerBlynkManager::sendStatus(const String& status) {
  if (blynk_connected) {
    Blynk.virtualWrite(VPIN_STATUS, status);
  }
}

void PowerBlynkManager::sendAlert(AlertType alert, const String& message) {
  if (!blynk_connected) return;
  
  // Check cooldown
  if (millis() - alert_cooldown[alert] < 60000) {  // 1 minute cooldown
    return;
  }
  
  // Send push notification
  Blynk.logEvent("power_alert", message);
  
  // Send to terminal
  sendTerminalMessage("ALERT: " + message);
  
  alert_sent[alert] = true;
  alert_cooldown[alert] = millis();
  
  DEBUG_PRINTF("Alert sent: %s\n", message.c_str());
}

void PowerBlynkManager::handleAlerts(float voltage, float current, float power) {
  // Check voltage thresholds
  if (voltage < alertSys.getVoltageMinThreshold()) {
    sendAlert(ALERT_VOLTAGE_LOW, 
              "Low voltage detected: " + String(voltage, 1) + "V");
  }
  
  if (voltage > alertSys.getVoltageMaxThreshold()) {
    sendAlert(ALERT_VOLTAGE_HIGH, 
              "High voltage detected: " + String(voltage, 1) + "V");
  }
  
  // Check current threshold
  if (current > alertSys.getCurrentMaxThreshold()) {
    sendAlert(ALERT_CURRENT_HIGH, 
              "High current detected: " + String(current, 2) + "A");
  }
  
  // Check power threshold
  if (power > alertSys.getPowerMaxThreshold()) {
    sendAlert(ALERT_POWER_HIGH, 
              "High power detected: " + String(power, 1) + "W");
  }
}

void PowerBlynkManager::sendTerminalMessage(const String& message) {
  if (blynk_connected) {
    String timestamp = String(millis() / 1000);
    Blynk.virtualWrite(VPIN_TERMINAL, "[" + timestamp + "] " + message + "\n");
  }
}

void PowerBlynkManager::updateThresholds(float vmin, float vmax, float cmax) {
  alertSys.setVoltageThresholds(vmin, vmax);
  alertSys.setCurrentThreshold(cmax);
  
  sendTerminalMessage("Thresholds updated: V(" + String(vmin, 1) + "-" + 
                     String(vmax, 1) + "), I(" + String(cmax, 2) + ")");
}

String PowerBlynkManager::getConnectionStatus() const {
  if (blynk_connected) {
    return "Connected to " + server_address;
  } else if (blynk_initialized) {
    return "Disconnected (Retries: " + String(connection_retry_count) + ")";
  } else {
    return "Not initialized";
  }
}

void PowerBlynkManager::run() {
  if (blynk_connected) {
    Blynk.run();
  }
}

void PowerBlynkManager::sync() {
  if (blynk_connected) {
    Blynk.syncAll();
    DEBUG_PRINTLN("Blynk virtual pins synchronized");
  }
}

bool PowerBlynkManager::isTimeToSend() const {
  return (millis() - last_send_time) >= BLYNK_SEND_INTERVAL;
}

void PowerBlynkManager::setCredentials(const String& token, const String& server, int port) {
  auth_token = token;
  server_address = server;
  server_port = port;
}

// ============================================================================
// Blynk Virtual Pin Handlers
// ============================================================================

BLYNK_WRITE(VPIN_VOLTAGE_MIN) {
  float threshold = param.asFloat();
  if (threshold > 0 && threshold < 300) {
    blynkMgr.updateThresholds(threshold, alertSys.getVoltageMaxThreshold(), 
                             alertSys.getCurrentMaxThreshold());
  }
}

BLYNK_WRITE(VPIN_VOLTAGE_MAX) {
  float threshold = param.asFloat();
  if (threshold > 0 && threshold < 300) {
    blynkMgr.updateThresholds(alertSys.getVoltageMinThreshold(), threshold, 
                             alertSys.getCurrentMaxThreshold());
  }
}

BLYNK_WRITE(VPIN_CURRENT_MAX) {
  float threshold = param.asFloat();
  if (threshold > 0 && threshold < 20) {
    blynkMgr.updateThresholds(alertSys.getVoltageMinThreshold(), 
                             alertSys.getVoltageMaxThreshold(), threshold);
  }
}

BLYNK_WRITE(VPIN_RESET_ENERGY) {
  if (param.asInt() == 1) {
    sensors.resetEnergy();
    blynkMgr.sendTerminalMessage("Energy counter reset");
    blynkMgr.forceDataUpdate();
  }
}

BLYNK_WRITE(VPIN_TERMINAL) {
  String input = param.asStr();
  if (input == "status") {
    blynkMgr.sendTerminalMessage("System Status: " + alertSys.getSystemStateString());
    blynkMgr.sendTerminalMessage("WiFi: Connected");
    blynkMgr.sendTerminalMessage("Blynk: " + blynkMgr.getConnectionStatus());
    blynkMgr.sendTerminalMessage(sensors.getStatusString());
  } else if (input == "reset") {
    blynkMgr.sendTerminalMessage("System restart requested...");
    delay(1000);
    ESP.restart();
  }
}

BLYNK_CONNECTED() {
  DEBUG_PRINTLN("Blynk connected event");
  blynkMgr.sendTerminalMessage("Device connected to Blynk");
}

BLYNK_DISCONNECTED() {
  DEBUG_PRINTLN("Blynk disconnected event");
}
