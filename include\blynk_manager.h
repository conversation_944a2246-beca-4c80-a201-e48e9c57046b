#ifndef BLYNK_MANAGER_H
#define BLYNK_MANAGER_H

#include "blynk_config.h"
#include "config.h"

// Forward declarations to avoid multiple definitions
class BlynkWifi;

// ============================================================================
// Blynk Cloud Integration Manager
// ============================================================================

class PowerBlynkManager {
private:
  bool blynk_connected;
  bool blynk_initialized;
  unsigned long last_send_time;
  unsigned long last_heartbeat;
  int connection_retry_count;
  
  // Blynk credentials
  String auth_token;
  String server_address;
  int server_port;
  
  // Data tracking
  float last_voltage_sent;
  float last_current_sent;
  float last_power_sent;
  bool force_update;
  
  // Alert management
  bool alert_sent[10];  // Track sent alerts to avoid spam
  unsigned long alert_cooldown[10];
  
  // Private methods
  bool shouldSendUpdate(float voltage, float current, float power);
  void sendSensorData(float voltage, float current, float power, float energy);
  void handleAlerts(float voltage, float current, float power);
  void resetAlertFlags();
  
public:
  // Constructor
  PowerBlynkManager();
  
  // Initialization
  bool begin(const String& token, const String& server = "blynk.cloud", int port = 80);
  void setupVirtualPins();
  
  // Connection management
  bool connect();
  bool isConnected() const { return blynk_connected; }
  void handleConnection();
  void reconnect();
  void disconnect();
  
  // Data transmission
  void sendData(float voltage, float current, float power, float energy);
  void sendStatus(const String& status);
  void sendAlert(AlertType alert, const String& message);
  void forceDataUpdate() { force_update = true; }
  
  // Virtual pin handlers (defined in implementation)
  static void handleVoltageMinThreshold();
  static void handleVoltageMaxThreshold();
  static void handleCurrentMaxThreshold();
  static void handleEnergyReset();
  static void handleTerminalInput();
  
  // Utility methods
  void sendTerminalMessage(const String& message);
  void updateThresholds(float vmin, float vmax, float cmax);
  String getConnectionStatus() const;
  
  // Configuration
  void setCredentials(const String& token, const String& server = "blynk.cloud", int port = 80);
  String getAuthToken() const { return auth_token; }
  
  // Maintenance
  void run();
  void sync();
  bool isTimeToSend() const;
};

// Blynk virtual pin handlers
BLYNK_WRITE(VPIN_VOLTAGE_MIN);
BLYNK_WRITE(VPIN_VOLTAGE_MAX);
BLYNK_WRITE(VPIN_CURRENT_MAX);
BLYNK_WRITE(VPIN_RESET_ENERGY);
BLYNK_WRITE(VPIN_TERMINAL);

// Blynk connection handlers
BLYNK_CONNECTED();
BLYNK_DISCONNECTED();

// Global Blynk manager instance
extern PowerBlynkManager blynkMgr;

#endif // BLYNK_MANAGER_H
