#ifndef BLYNK_CONFIG_H
#define BLYNK_CONFIG_H

// ============================================================================
// Blynk Configuration for ESP32 Power Monitor
// ============================================================================

// Blynk Template Configuration (Required for Blynk 2.0)
// Replace these with your actual values from Blynk Console
#define BLYNK_TEMPLATE_ID "TMPL6XXXXXXXx"
#define BLYNK_TEMPLATE_NAME "ESP32 Power Monitor"

// Default Blynk Auth Token (will be replaced by user configuration)
#define BLYNK_AUTH_TOKEN "YourAuthTokenHere-32CharactersLong"

// Blynk Server Configuration
#define BLYNK_DEFAULT_SERVER "blynk.cloud"
#define BLYNK_DEFAULT_PORT 80

// Blynk Connection Settings
#define BLYNK_HEARTBEAT 10
#define BLYNK_TIMEOUT_MS 5000
#define BLYNK_MAX_READBYTES 1024

// Blynk Print Settings (for debugging)
#define BLYNK_PRINT Serial
#define BLYNK_DEBUG

// Virtual Pin Definitions (from config.h)
#ifndef VPIN_VOLTAGE
#define VPIN_VOLTAGE          V0      // Real-time voltage
#define VPIN_CURRENT          V1      // Real-time current
#define VPIN_POWER            V2      // Calculated power
#define VPIN_ENERGY           V3      // Energy consumption
#define VPIN_STATUS           V4      // System status
#define VPIN_VOLTAGE_MIN      V5      // Minimum voltage threshold
#define VPIN_VOLTAGE_MAX      V6      // Maximum voltage threshold
#define VPIN_CURRENT_MAX      V7      // Maximum current threshold
#define VPIN_RESET_ENERGY     V8      // Reset energy counter button
#define VPIN_TERMINAL         V9      // Terminal widget for logs
#endif

// Blynk Update Intervals
#define BLYNK_SEND_INTERVAL   2000    // Send data every 2 seconds
#define BLYNK_SYNC_INTERVAL   30000   // Sync virtual pins every 30 seconds

// Blynk Connection Retry Settings
#define BLYNK_MAX_RETRIES     3       // Maximum connection retries
#define BLYNK_RETRY_DELAY     5000    // Delay between retries (ms)

// Blynk Data Validation
#define BLYNK_MIN_SEND_INTERVAL 1000  // Minimum interval between sends
#define BLYNK_MAX_STRING_LENGTH 64    // Maximum string length for Blynk

// Blynk Terminal Settings
#define BLYNK_TERMINAL_MAX_LINES 20   // Maximum lines in terminal
#define BLYNK_TERMINAL_BUFFER_SIZE 512 // Terminal buffer size

#endif // BLYNK_CONFIG_H
