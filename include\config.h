#ifndef CONFIG_H
#define CONFIG_H

// ============================================================================
// ESP32 Power Monitoring System Configuration
// ============================================================================

// Hardware Configuration - ESP32 Specific GPIO Pins
#define VOLTAGE_SENSOR_PIN    36    // ZMPT101B voltage sensor (GPIO36/A0 - ADC1_CH0)
#define CURRENT_SENSOR_PIN    39    // ACS712 current sensor (GPIO39/A3 - ADC1_CH3)
#define LED_BUILTIN_PIN       2     // Built-in LED for status indication (GPIO2)

// Sensor Calibration Constants
// ZMPT101B Voltage Sensor (AC)
#define VOLTAGE_CALIBRATION   234.26  // Calibration factor for voltage sensor
#define VOLTAGE_OFFSET        2500    // ADC offset (mV) for zero voltage
#define VOLTAGE_SAMPLES       100     // Number of samples for RMS calculation

// ACS712 Current Sensor (5A version)
#define CURRENT_CALIBRATION   185.0   // mV/A for ACS712-5A
#define CURRENT_OFFSET        2500    // ADC offset (mV) for zero current
#define CURRENT_SAMPLES       100     // Number of samples for RMS calculation

// ESP32 ADC Configuration
#define ADC_RESOLUTION        4096    // 12-bit ADC resolution (0-4095)
#define ADC_VOLTAGE           3300    // ADC reference voltage (mV)
#define ADC_ATTENUATION       ADC_ATTEN_DB_11 // 11dB attenuation for 0-3.3V range
#define ADC_WIDTH             ADC_WIDTH_BIT_12 // 12-bit resolution
#define ADC_CHANNEL_VOLTAGE   ADC1_CHANNEL_0  // GPIO36 = ADC1_CH0
#define ADC_CHANNEL_CURRENT   ADC1_CHANNEL_3  // GPIO39 = ADC1_CH3

// Safety Thresholds
#define VOLTAGE_MIN_THRESHOLD 200.0   // Minimum voltage (V) before alert
#define VOLTAGE_MAX_THRESHOLD 250.0   // Maximum voltage (V) before alert
#define CURRENT_MAX_THRESHOLD 4.0     // Maximum current (A) before alert
#define POWER_MAX_THRESHOLD   1000.0  // Maximum power (W) before alert

// Timing Configuration
#define SENSOR_READ_INTERVAL  1000    // Sensor reading interval (ms)
#define BLYNK_SEND_INTERVAL   2000    // Blynk data send interval (ms)
#define WIFI_TIMEOUT          30000   // WiFi connection timeout (ms)
#define BLYNK_TIMEOUT         10000   // Blynk connection timeout (ms)

// Blynk Virtual Pins
#define VPIN_VOLTAGE          V0      // Real-time voltage
#define VPIN_CURRENT          V1      // Real-time current
#define VPIN_POWER            V2      // Calculated power
#define VPIN_ENERGY           V3      // Energy consumption
#define VPIN_STATUS           V4      // System status
#define VPIN_VOLTAGE_MIN      V5      // Minimum voltage threshold
#define VPIN_VOLTAGE_MAX      V6      // Maximum voltage threshold
#define VPIN_CURRENT_MAX      V7      // Maximum current threshold
#define VPIN_RESET_ENERGY     V8      // Reset energy counter button
#define VPIN_TERMINAL         V9      // Terminal widget for logs

// WiFi Configuration
#define WIFI_CONFIG_PORTAL_TIMEOUT 180  // WiFiManager portal timeout (seconds)
#define WIFI_HOSTNAME         "ESP32-PowerMonitor"
#define WIFI_AP_NAME          "PowerMonitor-Setup"
#define WIFI_AP_PASSWORD      "12345678"

// System Configuration
#define SERIAL_BAUD_RATE      115200
#define WATCHDOG_TIMEOUT      8000    // Watchdog timeout (ms)
#define STATUS_LED_BLINK      500     // Status LED blink interval (ms)

// Data Filtering
#define VOLTAGE_FILTER_WEIGHT 0.1     // Low-pass filter weight for voltage
#define CURRENT_FILTER_WEIGHT 0.1     // Low-pass filter weight for current
#define MIN_STABLE_READINGS   5       // Minimum stable readings before sending

// Error Handling
#define MAX_WIFI_RETRIES      3       // Maximum WiFi reconnection attempts
#define MAX_BLYNK_RETRIES     3       // Maximum Blynk reconnection attempts
#define ERROR_RECOVERY_DELAY  5000    // Delay before retry (ms)

// Debug Configuration
#ifdef DEBUG
  #define DEBUG_PRINT(x)      Serial.print(x)
  #define DEBUG_PRINTLN(x)    Serial.println(x)
  #define DEBUG_PRINTF(...)   Serial.printf(__VA_ARGS__)
#else
  #define DEBUG_PRINT(x)
  #define DEBUG_PRINTLN(x)
  #define DEBUG_PRINTF(...)
#endif

// System States
enum SystemState {
  STATE_INITIALIZING,
  STATE_WIFI_CONNECTING,
  STATE_BLYNK_CONNECTING,
  STATE_RUNNING,
  STATE_ERROR,
  STATE_RECOVERY
};

// Alert Types
enum AlertType {
  ALERT_VOLTAGE_LOW,
  ALERT_VOLTAGE_HIGH,
  ALERT_CURRENT_HIGH,
  ALERT_POWER_HIGH,
  ALERT_SENSOR_ERROR,
  ALERT_WIFI_DISCONNECTED,
  ALERT_BLYNK_DISCONNECTED
};

#endif // CONFIG_H
