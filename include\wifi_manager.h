#ifndef WIFI_MANAGER_H
#define WIFI_MANAGER_H

#include <WiFi.h>
#include <WebServer.h>
#include <DNSServer.h>
#include <ArduinoJson.h>
#include <SPIFFS.h>
#include "config.h"

// ============================================================================
// WiFi Management Class with Captive Portal
// ============================================================================

class PowerWiFiManager {
private:
  WebServer server;
  DNSServer dnsServer;
  bool wifi_connected;
  bool config_portal_running;
  unsigned long last_connection_attempt;
  int connection_retry_count;
  
  // Configuration storage
  String blynk_token;
  String blynk_server;
  String device_name;
  
  // Private methods
  void setupCustomParameters();
  void saveConfigCallback();
  bool loadConfiguration();
  bool saveConfiguration();
  void resetConfiguration();
  
public:
  // Constructor
  PowerWiFiManager();
  
  // Initialization
  bool begin();
  void setupConfigPortal();
  
  // Connection management
  bool connectWiFi();
  bool isConnected() const { return wifi_connected; }
  void handleConnection();
  void reconnect();
  
  // Configuration methods
  String getBlynkToken() const { return blynk_token; }
  String getBlynkServer() const { return blynk_server; }
  String getDeviceName() const { return device_name; }
  
  void setBlynkToken(const String& token) { blynk_token = token; }
  void setBlynkServer(const String& server) { blynk_server = server; }
  void setDeviceName(const String& name) { device_name = name; }
  
  // Status methods
  String getWiFiStatus() const;
  String getIPAddress() const;
  int getSignalStrength() const;
  
  // Utility methods
  void startConfigPortal();
  void resetSettings();
  bool shouldSaveConfig() const;
  
  // Callbacks
  static void configModeCallback(WiFiManager *myWiFiManager);
  static void saveConfigCallback();
};

// Global WiFi manager instance
extern PowerWiFiManager wifiMgr;

#endif // WIFI_MANAGER_H
