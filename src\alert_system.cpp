#include "alert_system.h"

// Global alert system instance
AlertSystem alertSys;

// ============================================================================
// AlertSystem Implementation
// ============================================================================

AlertSystem::AlertSystem() {
  voltage_min_threshold = VOLTAGE_MIN_THRESHOLD;
  voltage_max_threshold = VOLTAGE_MAX_THRESHOLD;
  current_max_threshold = CURRENT_MAX_THRESHOLD;
  power_max_threshold = POWER_MAX_THRESHOLD;
  
  alert_cooldown_period = 60000;  // 1 minute cooldown
  
  last_led_toggle = 0;
  led_state = false;
  blink_pattern = LED_PATTERN_OFF;
  
  current_state = STATE_INITIALIZING;
  last_error_message = "";
  
  // Initialize alert tracking
  for (int i = 0; i < 8; i++) {
    alert_active[i] = false;
    alert_last_sent[i] = 0;
  }
}

void AlertSystem::begin() {
  DEBUG_PRINTLN("Initializing Alert System...");
  
  // Configure status LED
  pinMode(LED_BUILTIN_PIN, OUTPUT);
  digitalWrite(LED_BUILTIN_PIN, LOW);
  
  setSystemState(STATE_INITIALIZING);
  setLEDPattern(LED_PATTERN_SLOW_BLINK);
  
  DEBUG_PRINTLN("Alert System initialized");
}

void AlertSystem::setVoltageThresholds(float min_voltage, float max_voltage) {
  if (min_voltage > 0 && max_voltage > min_voltage) {
    voltage_min_threshold = min_voltage;
    voltage_max_threshold = max_voltage;
    
    DEBUG_PRINTF("Voltage thresholds set: %.1fV - %.1fV\n", 
                 min_voltage, max_voltage);
  }
}

void AlertSystem::setCurrentThreshold(float max_current) {
  if (max_current > 0) {
    current_max_threshold = max_current;
    DEBUG_PRINTF("Current threshold set: %.2fA\n", max_current);
  }
}

void AlertSystem::setPowerThreshold(float max_power) {
  if (max_power > 0) {
    power_max_threshold = max_power;
    DEBUG_PRINTF("Power threshold set: %.1fW\n", max_power);
  }
}

void AlertSystem::checkAlerts(float voltage, float current, float power) {
  // Check voltage low
  if (voltage < voltage_min_threshold) {
    if (!alert_active[ALERT_VOLTAGE_LOW] && isAlertCooldownExpired(ALERT_VOLTAGE_LOW)) {
      triggerAlert(ALERT_VOLTAGE_LOW, 
                   "Low voltage: " + String(voltage, 1) + "V (< " + 
                   String(voltage_min_threshold, 1) + "V)");
    }
  } else {
    clearAlert(ALERT_VOLTAGE_LOW);
  }
  
  // Check voltage high
  if (voltage > voltage_max_threshold) {
    if (!alert_active[ALERT_VOLTAGE_HIGH] && isAlertCooldownExpired(ALERT_VOLTAGE_HIGH)) {
      triggerAlert(ALERT_VOLTAGE_HIGH, 
                   "High voltage: " + String(voltage, 1) + "V (> " + 
                   String(voltage_max_threshold, 1) + "V)");
    }
  } else {
    clearAlert(ALERT_VOLTAGE_HIGH);
  }
  
  // Check current high
  if (current > current_max_threshold) {
    if (!alert_active[ALERT_CURRENT_HIGH] && isAlertCooldownExpired(ALERT_CURRENT_HIGH)) {
      triggerAlert(ALERT_CURRENT_HIGH, 
                   "High current: " + String(current, 2) + "A (> " + 
                   String(current_max_threshold, 2) + "A)");
    }
  } else {
    clearAlert(ALERT_CURRENT_HIGH);
  }
  
  // Check power high
  if (power > power_max_threshold) {
    if (!alert_active[ALERT_POWER_HIGH] && isAlertCooldownExpired(ALERT_POWER_HIGH)) {
      triggerAlert(ALERT_POWER_HIGH, 
                   "High power: " + String(power, 1) + "W (> " + 
                   String(power_max_threshold, 1) + "W)");
    }
  } else {
    clearAlert(ALERT_POWER_HIGH);
  }
}

void AlertSystem::checkSystemHealth() {
  // This method can be expanded to check various system health indicators
  // For now, it's a placeholder for future health monitoring features
}

void AlertSystem::triggerAlert(AlertType alert, const String& custom_message) {
  if (alert >= 8) return;  // Invalid alert type
  
  setAlertActive(alert, true);
  alert_last_sent[alert] = millis();
  
  String message = custom_message.length() > 0 ? custom_message : getAlertMessage(alert);
  
  DEBUG_PRINTF("ALERT TRIGGERED: %s\n", message.c_str());
  
  // Update LED pattern based on alert severity
  if (alert == ALERT_VOLTAGE_LOW || alert == ALERT_VOLTAGE_HIGH) {
    setLEDPattern(LED_PATTERN_FAST_BLINK);
  } else if (alert == ALERT_CURRENT_HIGH || alert == ALERT_POWER_HIGH) {
    setLEDPattern(LED_PATTERN_TRIPLE_BLINK);
  } else {
    setLEDPattern(LED_PATTERN_DOUBLE_BLINK);
  }
}

void AlertSystem::clearAlert(AlertType alert) {
  if (alert >= 8) return;  // Invalid alert type
  
  if (alert_active[alert]) {
    setAlertActive(alert, false);
    DEBUG_PRINTF("Alert cleared: %d\n", alert);
    
    // Update LED pattern if no more critical alerts
    if (!hasActiveAlerts()) {
      if (current_state == STATE_RUNNING) {
        setLEDPattern(LED_PATTERN_SOLID);
      } else {
        setLEDPattern(LED_PATTERN_SLOW_BLINK);
      }
    }
  }
}

void AlertSystem::clearAllAlerts() {
  for (int i = 0; i < 8; i++) {
    alert_active[i] = false;
  }
  
  DEBUG_PRINTLN("All alerts cleared");
  
  // Reset LED pattern
  if (current_state == STATE_RUNNING) {
    setLEDPattern(LED_PATTERN_SOLID);
  }
}

void AlertSystem::setSystemState(SystemState state) {
  if (current_state != state) {
    current_state = state;
    
    DEBUG_PRINTF("System state changed to: %s\n", getSystemStateString().c_str());
    
    // Update LED pattern based on state
    switch (state) {
      case STATE_INITIALIZING:
        setLEDPattern(LED_PATTERN_SLOW_BLINK);
        break;
      case STATE_WIFI_CONNECTING:
        setLEDPattern(LED_PATTERN_DOUBLE_BLINK);
        break;
      case STATE_BLYNK_CONNECTING:
        setLEDPattern(LED_PATTERN_TRIPLE_BLINK);
        break;
      case STATE_RUNNING:
        if (!hasActiveAlerts()) {
          setLEDPattern(LED_PATTERN_SOLID);
        }
        break;
      case STATE_ERROR:
        setLEDPattern(LED_PATTERN_FAST_BLINK);
        break;
      case STATE_RECOVERY:
        setLEDPattern(LED_PATTERN_SLOW_BLINK);
        break;
    }
  }
}

String AlertSystem::getSystemStateString() const {
  switch (current_state) {
    case STATE_INITIALIZING: return "Initializing";
    case STATE_WIFI_CONNECTING: return "WiFi Connecting";
    case STATE_BLYNK_CONNECTING: return "Blynk Connecting";
    case STATE_RUNNING: return "Running";
    case STATE_ERROR: return "Error";
    case STATE_RECOVERY: return "Recovery";
    default: return "Unknown";
  }
}

bool AlertSystem::hasActiveAlerts() const {
  for (int i = 0; i < 8; i++) {
    if (alert_active[i]) {
      return true;
    }
  }
  return false;
}

int AlertSystem::getActiveAlertCount() const {
  int count = 0;
  for (int i = 0; i < 8; i++) {
    if (alert_active[i]) {
      count++;
    }
  }
  return count;
}

String AlertSystem::getActiveAlertsString() const {
  String alerts = "";
  for (int i = 0; i < 8; i++) {
    if (alert_active[i]) {
      if (alerts.length() > 0) alerts += ", ";
      alerts += getAlertMessage((AlertType)i);
    }
  }
  return alerts.length() > 0 ? alerts : "None";
}

void AlertSystem::setLEDPattern(int pattern) {
  blink_pattern = pattern;
}

void AlertSystem::handleLED() {
  unsigned long current_time = millis();
  
  switch (blink_pattern) {
    case LED_PATTERN_OFF:
      digitalWrite(LED_BUILTIN_PIN, LOW);
      break;
      
    case LED_PATTERN_SOLID:
      digitalWrite(LED_BUILTIN_PIN, HIGH);
      break;
      
    case LED_PATTERN_SLOW_BLINK:
      if (current_time - last_led_toggle > 1000) {
        led_state = !led_state;
        digitalWrite(LED_BUILTIN_PIN, led_state);
        last_led_toggle = current_time;
      }
      break;
      
    case LED_PATTERN_FAST_BLINK:
      if (current_time - last_led_toggle > 250) {
        led_state = !led_state;
        digitalWrite(LED_BUILTIN_PIN, led_state);
        last_led_toggle = current_time;
      }
      break;
      
    case LED_PATTERN_DOUBLE_BLINK:
    case LED_PATTERN_TRIPLE_BLINK:
      // More complex patterns can be implemented here
      if (current_time - last_led_toggle > 500) {
        led_state = !led_state;
        digitalWrite(LED_BUILTIN_PIN, led_state);
        last_led_toggle = current_time;
      }
      break;
  }
}

void AlertSystem::setError(const String& error_message) {
  last_error_message = error_message;
  setSystemState(STATE_ERROR);
  DEBUG_PRINTF("System error: %s\n", error_message.c_str());
}

void AlertSystem::clearError() {
  last_error_message = "";
  if (current_state == STATE_ERROR) {
    setSystemState(STATE_RECOVERY);
  }
}

bool AlertSystem::isAlertCooldownExpired(AlertType alert) {
  return (millis() - alert_last_sent[alert]) > alert_cooldown_period;
}

void AlertSystem::setAlertActive(AlertType alert, bool active) {
  if (alert < 8) {
    alert_active[alert] = active;
  }
}

void AlertSystem::updateStatusLED() {
  handleLED();
}

String AlertSystem::getAlertMessage(AlertType alert, float value) {
  switch (alert) {
    case ALERT_VOLTAGE_LOW: return "Voltage Low";
    case ALERT_VOLTAGE_HIGH: return "Voltage High";
    case ALERT_CURRENT_HIGH: return "Current High";
    case ALERT_POWER_HIGH: return "Power High";
    case ALERT_SENSOR_ERROR: return "Sensor Error";
    case ALERT_WIFI_DISCONNECTED: return "WiFi Disconnected";
    case ALERT_BLYNK_DISCONNECTED: return "Blynk Disconnected";
    default: return "Unknown Alert";
  }
}

void AlertSystem::printStatus() const {
  DEBUG_PRINTLN("=== Alert System Status ===");
  DEBUG_PRINTF("System State: %s\n", getSystemStateString().c_str());
  DEBUG_PRINTF("Active Alerts: %d\n", getActiveAlertCount());
  DEBUG_PRINTF("Voltage Thresholds: %.1f - %.1f V\n", 
               voltage_min_threshold, voltage_max_threshold);
  DEBUG_PRINTF("Current Threshold: %.2f A\n", current_max_threshold);
  DEBUG_PRINTF("Power Threshold: %.1f W\n", power_max_threshold);
  if (last_error_message.length() > 0) {
    DEBUG_PRINTF("Last Error: %s\n", last_error_message.c_str());
  }
  DEBUG_PRINTLN("==========================");
}

String AlertSystem::getStatusJSON() const {
  String json = "{";
  json += "\"state\":\"" + getSystemStateString() + "\",";
  json += "\"alerts\":" + String(getActiveAlertCount()) + ",";
  json += "\"voltage_min\":" + String(voltage_min_threshold, 1) + ",";
  json += "\"voltage_max\":" + String(voltage_max_threshold, 1) + ",";
  json += "\"current_max\":" + String(current_max_threshold, 2) + ",";
  json += "\"power_max\":" + String(power_max_threshold, 1);
  json += "}";
  return json;
}
