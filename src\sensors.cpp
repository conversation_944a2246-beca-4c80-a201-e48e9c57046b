#include "sensors.h"
#include <math.h>

// Global sensor instance
PowerSensors sensors;

// ============================================================================
// PowerSensors Implementation
// ============================================================================

PowerSensors::PowerSensors() {
  voltage_raw = 0.0;
  current_raw = 0.0;
  voltage_filtered = 0.0;
  current_filtered = 0.0;
  power_calculated = 0.0;
  energy_total = 0.0;
  
  voltage_calibration = VOLTAGE_CALIBRATION;
  current_calibration = CURRENT_CALIBRATION;
  voltage_offset = VOLTAGE_OFFSET;
  current_offset = CURRENT_OFFSET;
  
  last_reading_time = 0;
  last_energy_update = 0;
  
  sensors_initialized = false;
  calibration_complete = false;
  stable_readings_count = 0;
}

bool PowerSensors::begin() {
  DEBUG_PRINTLN("Initializing power sensors...");
  
  // Configure ADC pins
  pinMode(VOLTAGE_SENSOR_PIN, INPUT);
  pinMode(CURRENT_SENSOR_PIN, INPUT);
  
  // Set ADC resolution and attenuation
  analogReadResolution(12);  // 12-bit resolution (0-4095)
  analogSetAttenuation(ADC_11db);  // For 0-3.3V range
  
  // Initial calibration
  calibrateSensors();
  
  sensors_initialized = true;
  DEBUG_PRINTLN("Power sensors initialized successfully");
  
  return true;
}

void PowerSensors::calibrateSensors() {
  DEBUG_PRINTLN("Starting sensor calibration...");
  
  // Take multiple readings for offset calibration
  float voltage_sum = 0;
  float current_sum = 0;
  int samples = 50;
  
  for (int i = 0; i < samples; i++) {
    voltage_sum += analogRead(VOLTAGE_SENSOR_PIN);
    current_sum += analogRead(CURRENT_SENSOR_PIN);
    delay(10);
  }
  
  // Calculate average offset values
  float voltage_adc_avg = voltage_sum / samples;
  float current_adc_avg = current_sum / samples;
  
  // Convert to mV
  voltage_offset = (voltage_adc_avg * ADC_VOLTAGE) / ADC_RESOLUTION;
  current_offset = (current_adc_avg * ADC_VOLTAGE) / ADC_RESOLUTION;
  
  calibration_complete = true;
  
  DEBUG_PRINTF("Calibration complete - Voltage offset: %.2f mV, Current offset: %.2f mV\n", 
               voltage_offset, current_offset);
}

bool PowerSensors::updateReadings() {
  if (!sensors_initialized) {
    return false;
  }
  
  unsigned long current_time = millis();
  if (current_time - last_reading_time < SENSOR_READ_INTERVAL) {
    return false;  // Not time for next reading yet
  }
  
  // Read RMS values
  float new_voltage = readVoltageRMS();
  float new_current = readCurrentRMS();
  
  // Validate readings
  if (!validateReadings()) {
    DEBUG_PRINTLN("Invalid sensor readings detected");
    return false;
  }
  
  // Store raw values
  voltage_raw = new_voltage;
  current_raw = new_current;
  
  // Apply filtering
  applyLowPassFilter();
  
  // Calculate power
  power_calculated = voltage_filtered * current_filtered;
  
  // Update energy (integrate power over time)
  if (last_energy_update > 0) {
    float time_hours = (current_time - last_energy_update) / 3600000.0;  // Convert ms to hours
    energy_total += power_calculated * time_hours;  // Wh
  }
  last_energy_update = current_time;
  
  last_reading_time = current_time;
  
  // Track stable readings
  if (isDataStable()) {
    stable_readings_count++;
  } else {
    stable_readings_count = 0;
  }
  
  return true;
}

float PowerSensors::readVoltageRMS() {
  float sum_squares = 0;
  int samples = VOLTAGE_SAMPLES;
  
  for (int i = 0; i < samples; i++) {
    // Read ADC value
    int adc_value = analogRead(VOLTAGE_SENSOR_PIN);
    
    // Convert to voltage (mV)
    float voltage_mv = (adc_value * ADC_VOLTAGE) / ADC_RESOLUTION;
    
    // Remove DC offset
    float ac_voltage = voltage_mv - voltage_offset;
    
    // Square the value
    sum_squares += ac_voltage * ac_voltage;
    
    delayMicroseconds(100);  // Small delay between samples
  }
  
  // Calculate RMS
  float rms_mv = sqrt(sum_squares / samples);
  
  // Convert to actual voltage using calibration factor
  float actual_voltage = (rms_mv * voltage_calibration) / 1000.0;  // Convert mV to V
  
  return actual_voltage;
}

float PowerSensors::readCurrentRMS() {
  float sum_squares = 0;
  int samples = CURRENT_SAMPLES;
  
  for (int i = 0; i < samples; i++) {
    // Read ADC value
    int adc_value = analogRead(CURRENT_SENSOR_PIN);
    
    // Convert to voltage (mV)
    float voltage_mv = (adc_value * ADC_VOLTAGE) / ADC_RESOLUTION;
    
    // Remove DC offset
    float ac_voltage = voltage_mv - current_offset;
    
    // Square the value
    sum_squares += ac_voltage * ac_voltage;
    
    delayMicroseconds(100);  // Small delay between samples
  }
  
  // Calculate RMS voltage
  float rms_mv = sqrt(sum_squares / samples);
  
  // Convert to current using ACS712 sensitivity (mV/A)
  float actual_current = rms_mv / current_calibration;
  
  // Ensure positive values and apply minimum threshold
  if (actual_current < 0.01) {
    actual_current = 0.0;  // Noise floor
  }
  
  return actual_current;
}

void PowerSensors::applyLowPassFilter() {
  // Apply exponential moving average filter
  voltage_filtered = (VOLTAGE_FILTER_WEIGHT * voltage_raw) + 
                    ((1.0 - VOLTAGE_FILTER_WEIGHT) * voltage_filtered);
  
  current_filtered = (CURRENT_FILTER_WEIGHT * current_raw) + 
                    ((1.0 - CURRENT_FILTER_WEIGHT) * current_filtered);
}

bool PowerSensors::validateReadings() {
  // Check for reasonable voltage range (50V to 300V)
  if (voltage_raw < 50.0 || voltage_raw > 300.0) {
    return false;
  }
  
  // Check for reasonable current range (0A to 10A)
  if (current_raw < 0.0 || current_raw > 10.0) {
    return false;
  }
  
  return true;
}

void PowerSensors::resetEnergy() {
  energy_total = 0.0;
  last_energy_update = millis();
  DEBUG_PRINTLN("Energy counter reset");
}

bool PowerSensors::isDataStable() const {
  return stable_readings_count >= MIN_STABLE_READINGS;
}

bool PowerSensors::sensorsHealthy() const {
  return sensors_initialized && calibration_complete && 
         (millis() - last_reading_time) < (SENSOR_READ_INTERVAL * 3);
}

String PowerSensors::getStatusString() const {
  String status = "Sensors: ";
  if (sensors_initialized) {
    status += "OK";
    if (calibration_complete) {
      status += " (Calibrated)";
    }
    if (isDataStable()) {
      status += " (Stable)";
    }
  } else {
    status += "ERROR";
  }
  return status;
}

void PowerSensors::printDiagnostics() const {
  DEBUG_PRINTLN("=== Sensor Diagnostics ===");
  DEBUG_PRINTF("Voltage: %.2f V (Raw: %.2f V)\n", voltage_filtered, voltage_raw);
  DEBUG_PRINTF("Current: %.2f A (Raw: %.2f A)\n", current_filtered, current_raw);
  DEBUG_PRINTF("Power: %.2f W\n", power_calculated);
  DEBUG_PRINTF("Energy: %.3f Wh\n", energy_total);
  DEBUG_PRINTF("Voltage Offset: %.2f mV\n", voltage_offset);
  DEBUG_PRINTF("Current Offset: %.2f mV\n", current_offset);
  DEBUG_PRINTF("Stable Readings: %d\n", stable_readings_count);
  DEBUG_PRINTF("Sensors Healthy: %s\n", sensorsHealthy() ? "Yes" : "No");
  DEBUG_PRINTLN("========================");
}
